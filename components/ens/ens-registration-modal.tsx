'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAccount } from 'wagmi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/lib/toast-context';
import { useAuth } from '@/lib/auth-context';
import { apiService } from '@/lib/api';
import { ApplicationWithApiKey } from '@/lib/api';
import {
  CheckCircleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  WalletIcon,
  GlobeIcon,
  ArrowUpDownIcon,
  LoaderIcon,
  XIcon,
  SettingsIcon
} from 'lucide-react';
// Note: getENSTokenId is now imported dynamically in the contract utils
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { ApplicationSelection } from '@/components/ens/application-selection';

interface ENSRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
  className?: string;
}

type ModalStep = 'connect' | 'select-app' | 'enter-ens' | 'transfer-ens' | 'store-registration';

interface ENSModalState {
  currentStep: ModalStep;
  ensName: string;
  selectedApplication: ApplicationWithApiKey | null;
  createdContractAddress?: string;
  chain: string;
  registrarTxHash?: string;
  transferTxHash?: string;
  isLoading: boolean;
  error?: string;
  registrarCompleted: boolean;
  transferCompleted: boolean;
  storeCompleted: boolean;
}

export function ENSRegistrationModal({ isOpen, onClose, onSuccess }: ENSRegistrationModalProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [state, setState] = useState<ENSModalState>({
    currentStep: 'connect',
    ensName: '',
    selectedApplication: null,
    chain: 'sepolia',
    isLoading: false,
    registrarCompleted: false,
    transferCompleted: false,
    storeCompleted: false
  });

  // Note: Removed transaction hooks since we're doing direct contract interactions
  // This eliminates the need for wagmi's useSendTransaction and useWaitForTransactionReceipt

  // Store ENS registration function
  const handleStoreENS = useCallback(async () => {
    if (!token || !state.ensName || !state.createdContractAddress || !state.selectedApplication) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Storing ENS registration ===');
      const storeParams = {
        ens_name: state.ensName,
        contractAddress: state.createdContractAddress,
        chain: state.chain,
        isActive: true
      };
      console.log('Store parameters:', JSON.stringify(storeParams, null, 2));

      // Step 3: Store ENS registration in backend using the created contract address
      const response = await apiService.registerENSRoot(
        storeParams,
        token,
        state.selectedApplication.appId
      );

      console.log('=== FRONTEND: Store ENS API response ===');
      console.log('Full response:', JSON.stringify(response, null, 2));

      if (!response.success) {
        console.error('Store ENS API call failed:', response.error);
        throw new Error(response.error || 'Failed to store ENS registration');
      }

      console.log('=== FRONTEND: ENS registration stored successfully ===');

      setState(prev => ({
        ...prev,
        isLoading: false,
        storeCompleted: true
      }));

      showToast({
        type: 'success',
        title: 'ENS Registration Complete! 🎉',
        description: `${state.ensName} has been successfully registered and linked to ${state.selectedApplication.name}`
      });

      // Close modal and refresh dashboard
      setTimeout(() => {
        onSuccess?.(response.data);
        onClose();
      }, 2000);

    } catch (error) {
      console.error('=== FRONTEND: Store ENS failed ===', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to store ENS registration'
      }));
      showToast({
        type: 'error',
        title: 'Storage Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }, [token, state.ensName, state.createdContractAddress, state.chain, state.selectedApplication, showToast, onSuccess, onClose]);



  // Auto-trigger storage when reaching store-registration step
  useEffect(() => {
    if (state.currentStep === 'store-registration' &&
        state.transferCompleted &&
        !state.storeCompleted &&
        !state.isLoading &&
        state.ensName &&
        state.createdContractAddress &&
        state.selectedApplication) {
      console.log('Auto-triggering ENS storage...');
      handleStoreENS();
    }
  }, [state.currentStep, state.transferCompleted, state.storeCompleted, state.isLoading, state.ensName, state.createdContractAddress, state.selectedApplication, handleStoreENS]);





  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setState({
        currentStep: isConnected ? 'select-app' : 'connect',
        ensName: '',
        selectedApplication: null,
        chain: 'sepolia',
        isLoading: false,
        registrarCompleted: false,
        transferCompleted: false,
        storeCompleted: false
      });
    }
  }, [isOpen, isConnected]);

  // Auto-advance from connect step when wallet is connected
  useEffect(() => {
    if (isConnected && state.currentStep === 'connect') {
      setState(prev => ({ ...prev, currentStep: 'select-app' }));
    }
  }, [isConnected, state.currentStep]);

  // Handle modal close with confirmation if mid-process
  const handleCloseModal = useCallback(() => {
    if (state.isLoading || state.registrarCompleted || state.transferCompleted) {
      const confirmClose = window.confirm(
        'You are in the middle of the ENS registration process. Are you sure you want to close? Your progress will be lost.'
      );
      if (!confirmClose) return;
    }
    onClose();
  }, [state.isLoading, state.registrarCompleted, state.transferCompleted, onClose]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        // Inline the close logic to avoid dependency issues
        if (state.isLoading || state.registrarCompleted || state.transferCompleted) {
          const confirmClose = window.confirm(
            'You are in the middle of the ENS registration process. Are you sure you want to close? Your progress will be lost.'
          );
          if (!confirmClose) return;
        }
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, state.isLoading, state.registrarCompleted, state.transferCompleted, onClose]);



  const handleTransferENS = async () => {
    if (!token || !state.ensName || !state.createdContractAddress || !isConnected || !state.selectedApplication) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Transferring ENS directly with contract ABI ===');

      // Show preparation status
      showToast({
        type: 'info',
        title: 'Transferring ENS',
        description: 'Transferring ENS ownership to registrar contract...'
      });

      // Validate that we have a contract address from the registrar creation
      if (!state.createdContractAddress) {
        throw new Error('No contract address available. Please complete registrar creation first.');
      }

      // Step 1: Validate ENS ownership and that it's wrapped
      const { checkNameWrapperOwnership } = await import('@/lib/ens-contract-utils');
      const ownershipCheck = await checkNameWrapperOwnership(state.ensName, address!, state.chain);

      if (!ownershipCheck.isWrapped) {
        throw new Error('ENS name must be wrapped in NameWrapper contract to transfer. Please wrap your ENS name first.');
      }

      if (!ownershipCheck.isOwner) {
        throw new Error(`You do not own this wrapped ENS name. Current owner: ${ownershipCheck.actualOwner}`);
      }

      console.log('ENS NameWrapper ownership validated:', ownershipCheck);

      // Step 2: Transfer ENS directly using contract ABI
      const { transferNameWrapperDirect } = await import('@/lib/ens-contract-utils');
      const result = await transferNameWrapperDirect(
        state.ensName,
        address!,
        state.createdContractAddress,
        state.chain
      );

      console.log('=== FRONTEND: ENS transfer completed successfully ===');
      console.log('Transaction hash:', result.transaction.hash);

      // Update state with success
      setState(prev => ({
        ...prev,
        transferTxHash: result.transaction.hash,
        currentStep: 'store-registration',
        isLoading: false,
        transferCompleted: true
      }));

      showToast({
        type: 'success',
        title: 'ENS Transfer Complete! 🎉',
        description: `${state.ensName} ownership transferred to registrar contract`
      });

    } catch (error) {
      console.error('Transfer ENS failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to transfer ENS'
      }));
      showToast({
        type: 'error',
        title: 'ENS Transfer Failed',
        description: error instanceof Error ? error.message : 'Failed to transfer ENS ownership'
      });
    }
  };

  // Enhanced debug function to test individual components
  const handleDebugTest = async () => {
    console.log('=== ENHANCED DEBUG TEST START ===');
    const testResults = [];

    try {
      // Test 1: Basic wallet connection
      console.log('Test 1: Wallet connection');
      console.log('isConnected:', isConnected);
      console.log('address:', address);

      if (!isConnected || !address) {
        throw new Error('Wallet not connected');
      }
      testResults.push('✅ Wallet connection: PASSED');

      // Test 2: Get signer with enhanced validation
      console.log('Test 2: Getting signer');
      const { getSigner } = await import('@/lib/ens-utils');
      const signer = await getSigner();
      const signerAddress = await signer.getAddress();
      console.log('Signer address:', signerAddress);

      if (signerAddress.toLowerCase() !== address.toLowerCase()) {
        throw new Error(`Signer address mismatch: expected ${address}, got ${signerAddress}`);
      }
      testResults.push('✅ Signer validation: PASSED');

      // Test 3: Enhanced network check
      console.log('Test 3: Network check');
      const network = await signer.provider?.getNetwork();
      console.log('Network:', network?.name, 'chainId:', network?.chainId);

      const expectedChainId = state.chain === 'mainnet' ? 1 : 11155111;
      if (network?.chainId !== BigInt(expectedChainId)) {
        throw new Error(`Wrong network. Expected ${state.chain} (${expectedChainId}), got ${network?.chainId}`);
      }
      testResults.push('✅ Network validation: PASSED');

      // Test 4: Enhanced ETH balance check
      console.log('Test 4: ETH balance');
      const balance = await signer.provider?.getBalance(await signer.getAddress()) || 0n;
      console.log('Balance:', balance.toString(), 'wei');
      console.log('Balance:', (Number(balance) / 1e18).toFixed(4), 'ETH');

      if (balance < 1000000000000000n) {
        console.warn('⚠️ Low ETH balance detected');
        testResults.push('⚠️ ETH balance: LOW (may cause issues)');
      } else {
        testResults.push('✅ ETH balance: SUFFICIENT');
      }

      // Test 5: Enhanced factory contract validation
      console.log('Test 5: Factory contract');
      const { FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI } = await import('@/lib/contracts/factory-contract');
      console.log('Factory address:', FACTORY_CONTRACT_ADDRESS);

      const factoryCode = await signer.provider?.getCode(FACTORY_CONTRACT_ADDRESS);
      console.log('Factory contract exists:', factoryCode !== '0x');
      console.log('Factory code length:', factoryCode?.length);

      if (!factoryCode || factoryCode === '0x') {
        throw new Error(`Factory contract not deployed at ${FACTORY_CONTRACT_ADDRESS}`);
      }
      testResults.push('✅ Factory contract: DEPLOYED');

      // Test 6: Enhanced ENS validation
      console.log('Test 6: ENS validation');
      const { isValidENSName, namehash } = await import('@/lib/ens-utils');
      console.log('ENS name:', state.ensName);
      console.log('Is valid ENS:', isValidENSName(state.ensName));

      if (!state.ensName) {
        testResults.push('⚠️ ENS name: NOT PROVIDED');
      } else if (!isValidENSName(state.ensName)) {
        testResults.push('❌ ENS name: INVALID FORMAT');
      } else {
        const parentNode = namehash(state.ensName.toLowerCase());
        console.log('Parent node hash:', parentNode);
        testResults.push('✅ ENS name: VALID');
      }

      // Test 7: Enhanced contract connectivity test
      console.log('Test 7: Contract connectivity test');
      const factoryContract = new (await import('ethers')).ethers.Contract(
        FACTORY_CONTRACT_ADDRESS,
        FACTORY_CONTRACT_ABI,
        signer
      );

      const allContracts = await factoryContract.getAllSubnameContracts();
      console.log('Total existing contracts:', allContracts.length);
      testResults.push('✅ Contract connectivity: PASSED');

      // Test 8: ENS ownership validation (if ENS name provided)
      if (state.ensName && isValidENSName(state.ensName)) {
        console.log('Test 8: ENS ownership validation');
        try {
          const { validateENSOwnership } = await import('@/lib/ens-contract-utils');
          const ownershipCheck = await validateENSOwnership(state.ensName, address, state.chain);
          console.log('Ownership check result:', ownershipCheck);

          if (ownershipCheck.isValid) {
            testResults.push('✅ ENS ownership: VERIFIED');
          } else {
            testResults.push(`❌ ENS ownership: FAILED - ${ownershipCheck.error}`);
          }
        } catch (ownershipError) {
          console.error('Ownership validation error:', ownershipError);
          testResults.push(`❌ ENS ownership: ERROR - ${ownershipError.message}`);
        }
      }

      // Test 9: Gas estimation test (if ENS name provided)
      if (state.ensName && isValidENSName(state.ensName)) {
        console.log('Test 9: Gas estimation test');
        try {
          const parentNode = namehash(state.ensName.toLowerCase());
          const gasEstimate = await factoryContract.createSubnameRegistrar.estimateGas(parentNode);
          console.log('Gas estimate:', gasEstimate.toString());
          testResults.push('✅ Gas estimation: PASSED');
        } catch (gasError) {
          console.error('Gas estimation error:', gasError);
          testResults.push(`❌ Gas estimation: FAILED - ${gasError.reason || gasError.message}`);
        }
      }

      // Display comprehensive results
      console.log('=== DEBUG TEST RESULTS ===');
      testResults.forEach(result => console.log(result));

      const passedTests = testResults.filter(r => r.startsWith('✅')).length;
      const totalTests = testResults.length;

      showToast({
        type: 'success',
        title: `Debug Test Complete: ${passedTests}/${totalTests} Passed`,
        description: 'Check console for detailed results.'
      });

    } catch (error) {
      console.error('=== DEBUG TEST FAILED ===');
      console.error('Error:', error);
      console.log('=== PARTIAL TEST RESULTS ===');
      testResults.forEach(result => console.log(result));

      showToast({
        type: 'error',
        title: 'Debug Test Failed',
        description: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  const handleCreateRegistrar = async () => {
    if (!token || !state.ensName || !isConnected || !state.selectedApplication) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Creating registrar with detailed debugging ===');
      console.log('ENS Name:', state.ensName);
      console.log('Chain:', state.chain);
      console.log('User Address:', address);
      console.log('Connected:', isConnected);
      console.log('Selected App:', state.selectedApplication?.name);

      // Show preparation status
      showToast({
        type: 'info',
        title: 'Creating Registrar',
        description: 'Validating ENS ownership and creating registrar...'
      });

      // Step 1: Enhanced basic validation
      if (!state.ensName.endsWith('.eth')) {
        throw new Error('ENS name must end with .eth');
      }

      if (!address) {
        throw new Error('Wallet not connected');
      }

      // Step 2: Enhanced signer validation with retry logic
      console.log('Getting wallet signer...');
      let signer: any;
      let signerAddress: string;

      try {
        const { getSigner } = await import('@/lib/ens-utils');
        signer = await getSigner();
        signerAddress = await signer.getAddress();
        console.log('Signer address:', signerAddress);

        if (signerAddress.toLowerCase() !== address.toLowerCase()) {
          throw new Error(`Signer address mismatch: expected ${address}, got ${signerAddress}`);
        }
      } catch (signerError) {
        console.error('Signer error:', signerError);
        throw new Error(`Failed to get wallet signer: ${signerError.message}. Please ensure your wallet is connected and unlocked.`);
      }

      // Step 3: Enhanced network and balance validation
      try {
        const network = await signer.provider?.getNetwork();
        console.log('Current network:', network?.name, 'chainId:', network?.chainId);

        // Validate we're on the correct network
        const expectedChainId = state.chain === 'mainnet' ? 1 : 11155111; // Sepolia
        if (network?.chainId !== BigInt(expectedChainId)) {
          throw new Error(`Wrong network. Please switch to ${state.chain === 'mainnet' ? 'Ethereum Mainnet' : 'Sepolia Testnet'}`);
        }

        const balance = await signer.provider?.getBalance(signerAddress) || 0n;
        console.log('User ETH balance:', balance.toString(), 'wei');
        console.log('User ETH balance:', (Number(balance) / 1e18).toFixed(4), 'ETH');

        if (balance < 1000000000000000n) { // 0.001 ETH minimum
          throw new Error('Insufficient ETH balance for gas fees. You need at least 0.001 ETH.');
        }
      } catch (networkError) {
        console.error('Network/balance error:', networkError);
        throw new Error(`Network validation failed: ${networkError.message}`);
      }

      // Step 4: Enhanced ENS ownership validation
      console.log('Validating ENS ownership...');
      try {
        const { validateENSOwnership } = await import('@/lib/ens-contract-utils');
        const ownershipCheck = await validateENSOwnership(state.ensName, address, state.chain);

        console.log('Ownership check result:', ownershipCheck);

        if (!ownershipCheck.isValid) {
          const errorMsg = ownershipCheck.error || 'You do not own this ENS name';
          throw new Error(`ENS ownership validation failed: ${errorMsg}`);
        }
      } catch (ownershipError) {
        console.error('Ownership validation error:', ownershipError);
        throw new Error(`ENS ownership validation failed: ${ownershipError.message}`);
      }

      // Step 5: Enhanced factory contract validation
      console.log('Validating factory contract...');
      try {
        const { FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI } = await import('@/lib/contracts/factory-contract');

        // Check if factory contract exists
        const factoryCode = await signer.provider?.getCode(FACTORY_CONTRACT_ADDRESS);
        if (!factoryCode || factoryCode === '0x') {
          throw new Error(`Factory contract not deployed at ${FACTORY_CONTRACT_ADDRESS} on ${state.chain}`);
        }
        console.log('Factory contract verified, code length:', factoryCode.length);

        // Test factory contract connectivity
        const factoryContract = new (await import('ethers')).ethers.Contract(
          FACTORY_CONTRACT_ADDRESS,
          FACTORY_CONTRACT_ABI,
          signer
        );

        // Try a simple read operation to test connectivity
        const allContracts = await factoryContract.getAllSubnameContracts();
        console.log('Factory contract connectivity test passed. Total contracts:', allContracts.length);
      } catch (factoryError) {
        console.error('Factory contract validation error:', factoryError);
        throw new Error(`Factory contract validation failed: ${factoryError.message}`);
      }

      // Step 6: Check for existing registrars
      console.log('Checking for existing registrars...');
      try {
        const { getUserRegistrarContracts } = await import('@/lib/ens-contract-utils');
        const existingContracts = await getUserRegistrarContracts(address, state.chain);
        console.log('Existing registrar contracts:', existingContracts);
      } catch (existingError) {
        console.warn('Could not check existing contracts:', existingError);
        // This is not a fatal error, continue
      }

      // Step 7: Create registrar with enhanced error handling and retry logic
      console.log('Creating registrar contract...');
      let result: any;
      try {
        const { createRegistrarWithRetry } = await import('@/lib/ens-error-recovery');

        // Use retry logic for registrar creation
        const retryResult = await createRegistrarWithRetry(state.ensName, state.chain, {
          maxRetries: 2, // Allow 2 retries for network issues
          baseDelay: 2000, // 2 second base delay
          maxDelay: 8000 // Max 8 second delay
        });

        if (!retryResult.success || !retryResult.result) {
          const { getEnhancedErrorMessage, getRecoveryAction } = await import('@/lib/ens-error-recovery');
          const enhancedMessage = getEnhancedErrorMessage(retryResult.error);
          const recoveryAction = getRecoveryAction(retryResult.error);

          const fullMessage = recoveryAction
            ? `${enhancedMessage} ${recoveryAction}`
            : enhancedMessage;

          throw new Error(fullMessage);
        }

        result = retryResult.result;

        console.log('=== FRONTEND: Registrar created successfully ===');
        console.log('Transaction hash:', result.transaction.hash);
        console.log('Contract address:', result.contractAddress);
        console.log('Transaction receipt:', result.receipt);
        console.log('Retry attempts:', retryResult.retryCount);
        console.log('Total time:', retryResult.totalTime, 'ms');

        if (!result.contractAddress) {
          throw new Error('Failed to get contract address from transaction receipt. The transaction may have succeeded but event parsing failed.');
        }
      } catch (createError) {
        console.error('Registrar creation error:', createError);
        throw createError; // Re-throw the enhanced error message
      }

      // Update state with success
      setState(prev => ({
        ...prev,
        registrarTxHash: result.transaction.hash,
        currentStep: 'transfer-ens',
        isLoading: false,
        registrarCompleted: true,
        createdContractAddress: result.contractAddress
      }));

      showToast({
        type: 'success',
        title: 'Registrar Created Successfully! 🎉',
        description: `Subname registrar contract created at ${result.contractAddress?.slice(0, 6)}...${result.contractAddress?.slice(-4)}`
      });

    } catch (error) {
      console.error('=== FRONTEND: Detailed registrar creation error ===');
      console.error('Error object:', error);
      console.error('Error message:', error instanceof Error ? error.message : 'Unknown error');
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');

      // Log additional error details if available
      if (error.transaction) {
        console.error('Failed transaction:', error.transaction);
      }
      if (error.receipt) {
        console.error('Transaction receipt:', error.receipt);
      }
      if (error.reason) {
        console.error('Revert reason:', error.reason);
      }
      if (error.code) {
        console.error('Error code:', error.code);
      }

      // Use enhanced error recovery utilities for better error messages
      let userFriendlyMessage: string;
      let recoveryAction: string | null = null;

      try {
        const { getEnhancedErrorMessage, getRecoveryAction } = await import('@/lib/ens-error-recovery');
        userFriendlyMessage = getEnhancedErrorMessage(error);
        recoveryAction = getRecoveryAction(error);
      } catch (importError) {
        // Fallback if import fails
        userFriendlyMessage = error instanceof Error ? error.message : 'Failed to create registrar contract';
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: userFriendlyMessage
      }));

      showToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: recoveryAction ? `${userFriendlyMessage} ${recoveryAction}` : userFriendlyMessage
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleCloseModal}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="sticky top-0 bg-white border-b border-[#B497D6]/20 rounded-t-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              Register ENS Domain
            </h2>
            <Button
              onClick={handleCloseModal}
              variant="outline"
              size="sm"
              className="border-gray-300 text-gray-600 hover:bg-gray-100"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Progress Indicator */}
          <div className="grid grid-cols-5 gap-2">
            {[
              { step: 'connect', label: 'Connect', stepNumber: 1 },
              { step: 'select-app', label: 'Select App', stepNumber: 2 },
              { step: 'enter-ens', label: 'ENS & Registrar', stepNumber: 3 },
              { step: 'transfer-ens', label: 'Transfer', stepNumber: 4 },
              { step: 'store-registration', label: 'Complete', stepNumber: 5 }
            ].map(({ step, label, stepNumber }) => {
              const stepOrder: ModalStep[] = ['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'];
              const currentIndex = stepOrder.indexOf(state.currentStep);
              const stepIndex = stepOrder.indexOf(step as ModalStep);
              const isCompleted = stepIndex < currentIndex;
              const isActive = stepIndex === currentIndex;

              return (
                <div key={step} className="flex flex-col items-center text-center">
                  <div className={`
                    flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all mb-1
                    ${isCompleted ? 'bg-green-500 border-green-500 text-white' : ''}
                    ${isActive ? 'bg-[#4A148C] border-[#4A148C] text-white' : ''}
                    ${!isActive && !isCompleted ? 'bg-gray-100 border-gray-300 text-gray-400' : ''}
                  `}>
                    {isCompleted ? (
                      <CheckCircleIcon className="h-4 w-4" />
                    ) : (
                      <span className="text-xs font-bold">{stepNumber}</span>
                    )}
                  </div>
                  <p className={`text-xs font-medium ${
                    isCompleted ? 'text-green-600' :
                    isActive ? 'text-[#4A148C]' : 'text-gray-400'
                  }`}>
                    {label}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Modal Content */}
        <div className="p-6">
          {/* Step 1: Connect Wallet */}
          {state.currentStep === 'connect' && (
            <div className="space-y-6">
              <div className="text-center">
                <WalletIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Connect Your Wallet</h3>
                <p className="text-gray-600 mb-6">
                  Connect your wallet to verify ENS ownership and interact with the blockchain
                </p>
              </div>

              <div className="flex justify-center">
                <ConnectButton />
              </div>

              {isConnected && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
                  <CheckCircleIcon className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <p className="text-green-700 font-medium">Wallet Connected Successfully!</p>
                  <p className="text-green-600 text-sm">Proceeding to application selection...</p>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Select Application */}
          {state.currentStep === 'select-app' && (
            <div className="space-y-6">
              <div className="text-center">
                <GlobeIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Select Application</h3>
                <p className="text-gray-600 mb-6">
                  Choose which application you want to register an ENS domain for
                </p>
              </div>

              <ApplicationSelection
                onApplicationSelect={(_, application) => {
                  setState(prev => ({
                    ...prev,
                    selectedApplication: application,
                    currentStep: 'enter-ens'
                  }));
                }}
                selectedApplicationId={state.selectedApplication?.appId ? String(state.selectedApplication.appId) : undefined}
              />
            </div>
          )}

          {/* Step 3: Enter ENS & Create Registrar */}
          {state.currentStep === 'enter-ens' && (
            <div className="space-y-6">
              <div className="text-center">
                <SettingsIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Enter ENS Name & Create Registrar</h3>
                <p className="text-gray-600 mb-6">
                  Enter your ENS name and create a subname registrar contract
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="ensName" className="text-sm font-medium text-gray-700">
                    ENS Name *
                  </Label>
                  <Input
                    id="ensName"
                    placeholder="myproject.eth"
                    value={state.ensName}
                    onChange={(e) => setState(prev => ({ ...prev, ensName: e.target.value }))}
                    className="mt-1 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]"
                  />
                  <div className="mt-2 space-y-1">
                    <p className="text-xs text-gray-500">
                      Enter the ENS name you own (e.g., myproject.eth, mydao.eth)
                    </p>
                    <p className="text-xs text-amber-600">
                      ⚠️ You must own this ENS name with the connected wallet
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="walletAddress" className="text-sm font-medium text-gray-700">
                    Connected Wallet
                  </Label>
                  <div className="mt-1 p-3 bg-gradient-to-r from-[#4A148C]/5 to-[#7B1FA2]/5 border border-[#B497D6]/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <code className="text-sm font-mono text-[#4A148C]">
                        {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
                      </code>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      This wallet must own the ENS name you want to register
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="chain" className="text-sm font-medium text-gray-700">
                    Blockchain Network *
                  </Label>
                  <select
                    id="chain"
                    value={state.chain}
                    onChange={(e) => setState(prev => ({ ...prev, chain: e.target.value }))}
                    className="mt-1 w-full px-3 py-2 border border-[#B497D6]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#4A148C] focus:border-[#4A148C]"
                  >
                    <option value="sepolia">Sepolia Testnet</option>
                    <option value="mainnet">Ethereum Mainnet</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Select the network where your ENS name is registered
                  </p>
                </div>
              </div>

              {/* Validation Summary */}
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Before Creating Registrar:</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li className="flex items-center gap-2">
                    {state.ensName ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-blue-400" />
                    )}
                    ENS name entered
                  </li>
                  <li className="flex items-center gap-2">
                    {isConnected ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-blue-400" />
                    )}
                    Wallet connected
                  </li>
                  <li className="flex items-center gap-2">
                    {state.ensName && state.ensName.endsWith('.eth') ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-blue-400" />
                    )}
                    Valid ENS format (.eth)
                  </li>
                </ul>
              </div>

              {/* Debug Information */}
              {false && process.env.NODE_ENV === 'development' && (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-xs">
                  <h5 className="font-medium text-gray-700 mb-1">Debug Info:</h5>
                  <div className="text-gray-600 space-y-1">
                    <div>ENS Name: {state.ensName || 'Not set'}</div>
                    <div>Chain: {state.chain}</div>
                    <div>From Address: {address ? String(address) : 'Not connected'}</div>
                    <div>App ID: {String(state.selectedApplication?.appId || 'Not set')}</div>
                    <div>Token Available: {token ? 'Yes' : 'No'}</div>
                    <div>Contract Address: {state.createdContractAddress || 'Not set'}</div>
                  </div>

                  {/* Test API Button */}
                  <Button
                    onClick={async () => {
                      if (!token || !state.selectedApplication || !state.ensName || !address) {
                        console.log('Missing required data for API test');
                        return;
                      }

                      console.log('=== TESTING API CALL ===');
                      try {
                        const response = await apiService.prepareRegistrarTransaction(
                          {
                            ensName: state.ensName,
                            chain: state.chain
                          },
                          token,
                          state.selectedApplication.appId
                        );
                        console.log('Test API Response:', JSON.stringify(response, null, 2));
                      } catch (error) {
                        console.error('Test API Error:', error);
                      }
                    }}
                    className="mt-2 text-xs px-2 py-1 bg-blue-500 text-white rounded"
                    disabled={!token || !state.selectedApplication || !state.ensName || !address}
                  >
                    Test API Call
                  </Button>
                </div>
              )}

              {/* Error Display */}
              {state.error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <XIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-red-800">
                      <p className="font-medium">Error Creating Registrar</p>
                      <p className="text-xs mt-1">{state.error}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Wallet Signing Instructions */}
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <WalletIcon className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">Wallet Signature Required</p>
                    <p className="text-xs mt-1">
                      You'll need to sign this transaction in your connected wallet to create the registrar contract.
                      Make sure you have enough ETH for gas fees.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    onClick={handleDebugTest}
                    variant="outline"
                    className="border-orange-500 text-orange-600 hover:bg-orange-50"
                  >
                    🔍 Debug Test
                  </Button>
                  <Button
                    onClick={() => window.open('/docs/ENS_TROUBLESHOOTING.md', '_blank')}
                    variant="outline"
                    className="border-blue-500 text-blue-600 hover:bg-blue-50"
                  >
                    📖 Help Guide
                  </Button>
                </div>

                <Button
                  onClick={handleCreateRegistrar}
                  disabled={!state.ensName || !isConnected || !state.ensName.endsWith('.eth') || state.isLoading || state.registrarCompleted}
                  className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {state.registrarCompleted ? (
                    <>
                      <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                      Registrar Created Successfully
                    </>
                  ) : state.isLoading ? (
                    <>
                      <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                      Creating Registrar Contract...
                    </>
                  ) : !state.ensName ? (
                    'Enter ENS Name to Continue'
                  ) : !isConnected ? (
                    'Connect Wallet First'
                  ) : !state.ensName.endsWith('.eth') ? (
                    'ENS Name Must End with .eth'
                  ) : (
                    'Create Subname Registrar Contract'
                  )}
                </Button>

                {/* Retry button for failed attempts */}
                {state.error && !state.isLoading && !state.registrarCompleted && (
                  <Button
                    onClick={() => {
                      setState(prev => ({ ...prev, error: undefined }));
                      handleCreateRegistrar();
                    }}
                    variant="outline"
                    className="w-full border-red-500 text-red-600 hover:bg-red-50"
                  >
                    🔄 Retry Registrar Creation
                  </Button>
                )}
              </div>

              {state.registrarCompleted && state.createdContractAddress && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-2">✅ Registrar Created Successfully!</h4>
                  <div className="text-xs text-green-700 space-y-1">
                    <p><strong>Contract Address:</strong></p>
                    <code className="block bg-green-100 p-2 rounded text-xs break-all">
                      {state.createdContractAddress}
                    </code>
                    <p className="mt-2">You can now proceed to transfer your ENS name to this contract.</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Transfer ENS to Contract */}
          {state.currentStep === 'transfer-ens' && (
            <div className="space-y-6">
              <div className="text-center">
                <ArrowUpDownIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Transfer ENS to Contract</h3>
                <p className="text-gray-600 mb-6">
                  Transfer ownership of your ENS name to the registrar contract
                </p>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                  <p className="text-sm font-medium text-blue-900 mb-1">ENS Name to Transfer</p>
                  <p className="text-xl font-bold text-blue-700">{state.ensName || 'Not specified'}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-sm font-medium text-gray-700 mb-2">From (Current Owner)</p>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <code className="text-sm font-mono text-gray-900">
                        {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
                      </code>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Your wallet address</p>
                  </div>

                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-700 mb-2">To (New Owner)</p>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${state.createdContractAddress ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      <code className="text-sm font-mono text-green-900">
                        {state.createdContractAddress
                          ? `${state.createdContractAddress.slice(0, 6)}...${state.createdContractAddress.slice(-4)}`
                          : 'Waiting for contract...'
                        }
                      </code>
                    </div>
                    <p className="text-xs text-green-600 mt-1">Registrar contract address</p>
                  </div>
                </div>
              </div>

              {/* Wallet Signing Instructions for Transfer */}
              {state.registrarCompleted && !state.transferCompleted && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <ArrowUpDownIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">Transfer ENS Ownership</p>
                      <p className="text-xs mt-1">
                        You'll need to sign a transaction to transfer your ENS name ownership to the registrar contract.
                        This enables subname claiming functionality.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <Button
                onClick={handleTransferENS}
                disabled={state.isLoading || !state.createdContractAddress || !state.registrarCompleted || state.transferCompleted}
                className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {state.transferCompleted ? (
                  <>
                    <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                    ENS Transfer Completed Successfully
                  </>
                ) : state.isLoading ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Transferring ENS Ownership...
                  </>
                ) : !state.registrarCompleted ? (
                  'Complete Step 3: Create Registrar First'
                ) : !state.createdContractAddress ? (
                  'Waiting for Contract Address...'
                ) : (
                  'Transfer ENS Name to Contract'
                )}
              </Button>

              {state.transferCompleted && state.transferTxHash && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-2">✅ Transfer Completed Successfully!</h4>
                  <div className="text-xs text-green-700 space-y-1">
                    <p><strong>Transaction Hash:</strong></p>
                    <code className="block bg-green-100 p-2 rounded text-xs break-all">
                      {state.transferTxHash}
                    </code>
                    <p className="mt-2">Your ENS name has been transferred to the registrar contract. Proceeding to store registration...</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 5: Store Registration */}
          {state.currentStep === 'store-registration' && (
            <div className="space-y-6">
              <div className="text-center">
                <CheckCircleIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Store Registration</h3>
                <p className="text-gray-600 mb-6">
                  Save your ENS registration details in the Crefy Connect backend
                </p>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">📝 Registration Summary</h4>
                <div className="text-sm text-blue-700 space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">ENS Name:</p>
                      <p className="font-mono text-xs bg-blue-100 p-1 rounded">{state.ensName}</p>
                    </div>
                    <div>
                      <p className="font-medium">Application:</p>
                      <p className="text-xs">{state.selectedApplication?.name}</p>
                    </div>
                    <div>
                      <p className="font-medium">Contract Address:</p>
                      <p className="font-mono text-xs bg-blue-100 p-1 rounded break-all">
                        {state.createdContractAddress || 'Not available'}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium">Network:</p>
                      <p className="text-xs capitalize">{state.chain}</p>
                    </div>
                  </div>
                </div>
              </div>

              {state.isLoading ? (
                <div className="flex flex-col items-center justify-center py-8 space-y-4">
                  <LoaderIcon className="h-8 w-8 animate-spin text-[#4A148C]" />
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">Storing Registration...</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Saving your ENS integration details to Crefy Connect backend
                    </p>
                  </div>
                </div>
              ) : state.storeCompleted ? (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-2">✅ Registration Stored Successfully!</h4>
                  <p className="text-sm text-green-700">
                    Your ENS integration has been completed and saved. Redirecting to dashboard...
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <h4 className="text-sm font-medium text-amber-900 mb-2">⏳ Ready to Store Registration</h4>
                  <p className="text-sm text-amber-700">
                    All previous steps completed successfully. The registration will be stored automatically.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Modal Navigation */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <Button
              onClick={() => {
                const stepOrder: ModalStep[] = ['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'];
                const currentIndex = stepOrder.indexOf(state.currentStep);
                if (currentIndex > 0) {
                  setState(prev => ({ ...prev, currentStep: stepOrder[currentIndex - 1] }));
                }
              }}
              variant="outline"
              disabled={state.currentStep === 'connect' || state.isLoading}
              className="border-[#B497D6]/30 text-[#4A148C] hover:bg-[#4A148C]/5"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="text-sm text-gray-500">
              Step {['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'].indexOf(state.currentStep) + 1} of 5
            </div>

            <Button
              onClick={() => {
                const stepOrder: ModalStep[] = ['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'];
                const currentIndex = stepOrder.indexOf(state.currentStep);
                if (currentIndex < stepOrder.length - 1) {
                  setState(prev => ({ ...prev, currentStep: stepOrder[currentIndex + 1] }));
                }
              }}
              variant="outline"
              disabled={
                state.currentStep === 'store-registration' ||
                state.isLoading ||
                (state.currentStep === 'connect' && !isConnected) ||
                (state.currentStep === 'select-app' && !state.selectedApplication) ||
                (state.currentStep === 'enter-ens' && !state.registrarCompleted) ||
                (state.currentStep === 'transfer-ens' && !state.transferCompleted)
              }
              className="border-[#B497D6]/30 text-[#4A148C] hover:bg-[#4A148C]/5"
            >
              Next
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
