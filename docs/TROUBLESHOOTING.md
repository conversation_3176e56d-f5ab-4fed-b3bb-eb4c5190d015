# ENS Registration Troubleshooting Guide

## Common Error: "Registrar creation failed"

This error can occur for several reasons. Follow this troubleshooting guide to identify and resolve the issue.

### 1. Check ENS Ownership

**Problem**: User doesn't own the ENS name they're trying to register.

**Solution**:
```typescript
// Use the new validation function
import { validateENSOwnership } from '@/lib/ens-contract-utils';

const ownershipCheck = await validateENSOwnership(ensName, userAddress, chain);
if (!ownershipCheck.isValid) {
  console.error('Ownership error:', ownershipCheck.error);
  // Show user-friendly error message
}
```

**Common ownership issues**:
- ENS name is owned by a different address
- ENS name is not registered
- ENS name is owned by a contract (not the user's wallet)

### 2. Check Network Configuration

**Problem**: Wrong network or RPC issues.

**Solution**:
- Ensure user is connected to the correct network (Sepolia for testing, Mainnet for production)
- Verify RPC endpoints are working
- Check if the Factory contract is deployed on the target network

```typescript
// Verify network
const chainId = await signer.getChainId();
console.log('Current chain ID:', chainId);
console.log('Expected chain ID:', getChainId(chain));
```

### 3. Check Gas and Balance

**Problem**: Insufficient ETH for gas fees.

**Solution**:
```typescript
// Check user's ETH balance
const balance = await signer.getBalance();
console.log('User balance:', ethers.formatEther(balance), 'ETH');

// Estimate gas for the transaction
const gasEstimate = await factoryContract.createSubnameRegistrar.estimateGas(parentNode);
console.log('Estimated gas:', gasEstimate.toString());
```

### 4. Check Contract Addresses

**Problem**: Factory contract address is incorrect or contract is not deployed.

**Solution**:
```typescript
// Verify factory contract exists
const factoryCode = await provider.getCode(FACTORY_CONTRACT_ADDRESS);
if (factoryCode === '0x') {
  console.error('Factory contract not deployed at address:', FACTORY_CONTRACT_ADDRESS);
}
```

### 5. Check ENS Name Format

**Problem**: Invalid ENS name format.

**Solution**:
```typescript
import { isValidENSName } from '@/lib/ens-utils';

if (!isValidENSName(ensName)) {
  console.error('Invalid ENS name format:', ensName);
  // ENS name must end with .eth, .test, etc.
}
```

### 6. Debug Transaction Failure

**Problem**: Transaction reverts or fails.

**Solution**:
```typescript
try {
  const tx = await factoryContract.createSubnameRegistrar(parentNode);
  const receipt = await tx.wait();
  console.log('Transaction successful:', receipt.hash);
} catch (error) {
  console.error('Transaction failed:', error);
  
  // Check if it's a revert with reason
  if (error.reason) {
    console.error('Revert reason:', error.reason);
  }
  
  // Check if it's a gas estimation error
  if (error.code === 'UNPREDICTABLE_GAS_LIMIT') {
    console.error('Gas estimation failed - transaction would likely revert');
  }
}
```

## Debugging Steps

### Step 1: Enable Detailed Logging
Add comprehensive logging to track the registration process:

```typescript
const handleCreateRegistrar = async () => {
  console.log('=== REGISTRAR CREATION DEBUG ===');
  console.log('ENS Name:', state.ensName);
  console.log('Chain:', state.chain);
  console.log('User Address:', address);
  console.log('Factory Address:', FACTORY_CONTRACT_ADDRESS);
  
  try {
    // Validate ownership
    const ownershipCheck = await validateENSOwnership(state.ensName, address!, state.chain);
    console.log('Ownership check:', ownershipCheck);
    
    if (!ownershipCheck.isValid) {
      throw new Error(ownershipCheck.error);
    }
    
    // Create registrar
    const result = await createRegistrarDirect(state.ensName, state.chain);
    console.log('Registrar creation result:', result);
    
  } catch (error) {
    console.error('Detailed error:', error);
    // Log additional error details
    if (error.transaction) {
      console.error('Failed transaction:', error.transaction);
    }
    if (error.receipt) {
      console.error('Transaction receipt:', error.receipt);
    }
  }
};
```

### Step 2: Test with Known Working ENS
Use a test ENS name that you definitely own:

```typescript
// Test with a simple ENS name you own
const testEnsName = 'yourtest.eth'; // Replace with your actual ENS
const ownershipCheck = await validateENSOwnership(testEnsName, address, 'sepolia');
console.log('Test ownership result:', ownershipCheck);
```

### Step 3: Check Contract Events
Monitor contract events to see what's happening:

```typescript
// Listen for SubnameContractCreated events
factoryContract.on('SubnameContractCreated', (owner, contractAddress, parentNode, event) => {
  console.log('Registrar created:', {
    owner,
    contractAddress,
    parentNode,
    transactionHash: event.transactionHash
  });
});
```

### Step 4: Verify ABI and Contract Version
Ensure you're using the correct ABI:

```typescript
// Test a simple contract call
try {
  const allContracts = await factoryContract.getAllSubnameContracts();
  console.log('Factory contract is working, total contracts:', allContracts.length);
} catch (error) {
  console.error('Factory contract ABI mismatch or contract issue:', error);
}
```

## Common Solutions

### Solution 1: Switch to Direct Contract Interaction
If the old approach was failing, the new direct contract interaction should resolve most issues:

```typescript
// Old approach (removed)
// const txData = await prepareRegistrarTransaction(ensName, chain);
// sendRegistrarTx(txData);

// New approach (direct)
const result = await createRegistrarDirect(ensName, chain);
```

### Solution 2: Validate Before Action
Always validate before attempting operations:

```typescript
// Comprehensive validation
const validation = await validateENSOwnership(ensName, userAddress, chain);
if (!validation.isValid) {
  showToast({
    type: 'error',
    title: 'Validation Failed',
    description: validation.error
  });
  return;
}
```

### Solution 3: Handle Network Issues
Implement retry logic for network issues:

```typescript
const createRegistrarWithRetry = async (ensName, chain, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await createRegistrarDirect(ensName, chain);
    } catch (error) {
      console.warn(`Attempt ${i + 1} failed:`, error.message);
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // Exponential backoff
    }
  }
};
```

## Error Messages and User Feedback

### Improved Error Messages
Provide clear, actionable error messages to users:

```typescript
const getErrorMessage = (error) => {
  if (error.message.includes('insufficient funds')) {
    return 'Insufficient ETH balance for gas fees. Please add ETH to your wallet.';
  }
  if (error.message.includes('user rejected')) {
    return 'Transaction was cancelled. Please try again and approve the transaction.';
  }
  if (error.message.includes('not the owner')) {
    return 'You do not own this ENS name. Please check the ENS name or connect the correct wallet.';
  }
  if (error.message.includes('network')) {
    return 'Network connection issue. Please check your internet connection and try again.';
  }
  return 'Registration failed. Please try again or contact support if the issue persists.';
};
```

## Testing Checklist

Before deploying, test these scenarios:

- [ ] Valid ENS name owned by user
- [ ] Invalid ENS name format
- [ ] ENS name not owned by user
- [ ] Insufficient gas/ETH balance
- [ ] Network connectivity issues
- [ ] Contract not deployed on network
- [ ] User rejects transaction
- [ ] Transaction timeout
- [ ] Already created registrar for ENS name

## Support Information

If issues persist after following this guide:

1. **Check Console Logs**: Look for detailed error messages in browser console
2. **Verify Network**: Ensure correct network (Sepolia/Mainnet) is selected
3. **Check ENS Ownership**: Verify ENS ownership on ENS app or Etherscan
4. **Test with Different ENS**: Try with a different ENS name you own
5. **Contact Support**: Provide console logs and transaction details
