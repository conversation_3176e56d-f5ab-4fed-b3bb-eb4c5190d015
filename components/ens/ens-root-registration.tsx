'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { useAccount } from 'wagmi';
import {
  CheckCircleIcon,
  AlertCircleIcon,
  ExternalLinkIcon,
  GlobeIcon,
  ShieldCheckIcon
} from "lucide-react";
import { apiService } from "@/lib/api";
import { useAuth } from "@/lib/auth-context";
import {
  ENSRootRegistrationRequest,
  ENSRootRegistrationProps,
  SupportedChain,
  SUPPORTED_CHAINS
} from "@/lib/types/ens";
import { getENSOwner, isValidENSName } from "@/lib/ens-utils";
import { E<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON> } from "@/lib/ens-error-handler";
import { useWalletAuth } from "@/lib/services/wallet-auth";
import { ethers } from 'ethers';

export function ENSRootRegistration({
  applicationId,
  prefilledENSName,
  onSuccess,
  onError,
  className = ""
}: ENSRootRegistrationProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  const { authenticateWallet, isSigning } = useWalletAuth(applicationId, 'ens-registration');

  const [formData, setFormData] = useState({
    ensName: prefilledENSName || '',
    chain: 'sepolia' as SupportedChain,
    contractAddress: address || '', // Always use connected wallet address as default
  });

  const [isRegistering, setIsRegistering] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [ownershipVerified, setOwnershipVerified] = useState<boolean | null>(null);
  const [currentOwner, setCurrentOwner] = useState<string | null>(null);
  const [ensNameError, setEnsNameError] = useState<string | null>(null);
  const [isValidatingFormat, setIsValidatingFormat] = useState(false);
  const [registrationStatus, setRegistrationStatus] = useState<'idle' | 'validating' | 'submitting' | 'success' | 'error'>('idle');
  const [registrationError, setRegistrationError] = useState<string | null>(null);

  // Auto-populate contract address with connected wallet address
  useEffect(() => {
    if (address) {
      setFormData(prev => ({
        ...prev,
        contractAddress: address
      }));
    }
  }, [address]);

  // Handle prefilled data - if ENS name is provided from Step 3, mark as verified
  useEffect(() => {
    if (prefilledENSName && address) {
      setOwnershipVerified(true);
      setCurrentOwner(address);
      setEnsNameError(null);
    }
  }, [prefilledENSName, address]);

  // Reset verification when ENS name changes (but not for prefilled data)
  useEffect(() => {
    // Don't reset verification if we're using prefilled data and the name matches
    if (prefilledENSName && formData.ensName === prefilledENSName) {
      return;
    }

    setOwnershipVerified(null);
    setCurrentOwner(null);
    setEnsNameError(null);
  }, [formData.ensName, prefilledENSName]);

  // Real-time ENS name validation
  useEffect(() => {
    const validateENSName = async () => {
      if (!formData.ensName || formData.ensName === '.eth') {
        setEnsNameError(null);
        return;
      }

      setIsValidatingFormat(true);

      // Basic format validation
      if (!formData.ensName.endsWith('.eth')) {
        setEnsNameError('ENS name must end with .eth');
        setIsValidatingFormat(false);
        return;
      }

      const nameWithoutEth = formData.ensName.replace('.eth', '');

      // Length validation
      if (nameWithoutEth.length < 3) {
        setEnsNameError('ENS name must be at least 3 characters long');
        setIsValidatingFormat(false);
        return;
      }

      if (nameWithoutEth.length > 63) {
        setEnsNameError('ENS name must be less than 63 characters long');
        setIsValidatingFormat(false);
        return;
      }

      // Character validation
      const validCharacters = /^[a-z0-9-]+$/;
      if (!validCharacters.test(nameWithoutEth)) {
        setEnsNameError('ENS name can only contain lowercase letters, numbers, and hyphens');
        setIsValidatingFormat(false);
        return;
      }

      // Cannot start or end with hyphen
      if (nameWithoutEth.startsWith('-') || nameWithoutEth.endsWith('-')) {
        setEnsNameError('ENS name cannot start or end with a hyphen');
        setIsValidatingFormat(false);
        return;
      }

      // Reserved names check
      const reservedNames = ['www', 'mail', 'ftp', 'admin', 'root', 'api', 'app', 'test'];
      if (reservedNames.includes(nameWithoutEth.toLowerCase())) {
        setEnsNameError('This name is reserved and cannot be used');
        setIsValidatingFormat(false);
        return;
      }

      setEnsNameError(null);
      setIsValidatingFormat(false);
    };

    const timeoutId = setTimeout(validateENSName, 300); // Debounce validation
    return () => clearTimeout(timeoutId);
  }, [formData.ensName]);



  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'ensName' ? value.toLowerCase() : value
    }));
  };

  const handleENSNameChange = (value: string) => {
    // Ensure the value always ends with .eth
    let ensName = value.toLowerCase();
    if (!ensName.endsWith('.eth')) {
      ensName = ensName + '.eth';
    }

    // Remove any double .eth suffixes
    ensName = ensName.replace(/\.eth\.eth$/, '.eth');

    handleInputChange('ensName', ensName);
  };

  const verifyENSOwnership = async () => {
    // Pre-validation checks
    if (!formData.ensName || ensNameError || isValidatingFormat) {
      showToast({
        type: 'error',
        title: 'Invalid ENS Name',
        description: ensNameError || 'Please enter a valid .eth domain name'
      });
      return;
    }

    if (!isValidENSName(formData.ensName)) {
      showToast({
        type: 'error',
        title: 'Invalid ENS Format',
        description: 'Please enter a valid .eth domain name'
      });
      return;
    }

    if (!address) {
      showToast({
        type: 'error',
        title: 'Wallet Not Connected',
        description: 'Please connect your wallet to verify ownership'
      });
      return;
    }

    setIsVerifying(true);
    setOwnershipVerified(null);
    setCurrentOwner(null);

    try {
      // Use multiple providers for better reliability
      const providers = [
        new ethers.JsonRpcProvider('https://eth.llamarpc.com'),
        new ethers.JsonRpcProvider('https://rpc.ankr.com/eth'),
        new ethers.JsonRpcProvider('https://ethereum.publicnode.com')
      ];

      let owner: string | null = null;
      let lastError: Error | null = null;

      // Try each provider until one succeeds
      for (const provider of providers) {
        try {
          owner = await getENSOwner(formData.ensName, provider);
          if (owner !== null) break;
        } catch (error) {
          lastError = error as Error;
          console.warn(`Provider failed for ${formData.ensName}:`, error);
          continue;
        }
      }

      if (!owner) {
        setOwnershipVerified(false);
        setCurrentOwner(null);

        if (lastError) {
          showToast({
            type: 'error',
            title: 'Verification Failed',
            description: 'Unable to verify ENS ownership. Please check your connection and try again.'
          });
        } else {
          showToast({
            type: 'error',
            title: 'ENS Not Found',
            description: 'This ENS name does not exist or has no owner. Please check the name and try again.'
          });
        }
        return;
      }

      setCurrentOwner(owner);
      const isOwned = owner.toLowerCase() === address.toLowerCase();
      setOwnershipVerified(isOwned);

      if (isOwned) {
        showToast({
          type: 'success',
          title: 'Ownership Verified ✓',
          description: `You own ${formData.ensName}. You can proceed with registration.`
        });
      } else {
        showToast({
          type: 'error',
          title: 'Ownership Verification Failed',
          description: `You don't own ${formData.ensName}. Current owner: ${owner.slice(0, 6)}...${owner.slice(-4)}`
        });
      }
    } catch (error) {
      console.error('ENS verification error:', error);
      setOwnershipVerified(false);
      setCurrentOwner(null);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showToast({
        type: 'error',
        title: 'Verification Failed',
        description: `Failed to verify ENS ownership: ${errorMessage}`
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleRegister = async () => {
    // Pre-registration validation
    if (!token) {
      setRegistrationError('Authentication required. Please log in to register an ENS root.');
      showToast({
        type: 'error',
        title: 'Authentication Required',
        description: 'Please log in to register an ENS root'
      });
      return;
    }

    // Validate token format (basic JWT check)
    if (!token.includes('.') || token.split('.').length !== 3) {
      setRegistrationError('Invalid authentication token. Please log out and log back in.');
      showToast({
        type: 'error',
        title: 'Invalid Token',
        description: 'Your authentication token is invalid. Please log out and log back in.'
      });
      return;
    }

    if (!ownershipVerified) {
      setRegistrationError('ENS ownership must be verified before registration.');
      showToast({
        type: 'error',
        title: 'Ownership Not Verified',
        description: 'Please verify ENS ownership before registering'
      });
      return;
    }

    if (!formData.contractAddress || !ethers.isAddress(formData.contractAddress)) {
      setRegistrationError('Invalid contract address provided.');
      showToast({
        type: 'error',
        title: 'Invalid Contract Address',
        description: 'Please enter a valid Ethereum address'
      });
      return;
    }

    if (ensNameError || isValidatingFormat) {
      setRegistrationError('Please fix ENS name validation errors before proceeding.');
      showToast({
        type: 'error',
        title: 'Invalid ENS Name',
        description: 'Please fix validation errors before proceeding'
      });
      return;
    }

    setIsRegistering(true);
    setRegistrationStatus('validating');
    setRegistrationError(null);

    try {
      // Optional: Test if we can access applications with this token
      // Skip validation if backend is unreachable to avoid blocking ENS registration
      try {
        console.log('🧪 Testing token with applications endpoint...');
        const appsResponse = await apiService.getApplications(token);
        console.log('Applications response:', appsResponse);

        if (appsResponse.success) {
          // Check if the applicationId exists in the user's applications
          const apps = appsResponse.data || [];
          const appExists = apps.some((app: any) => app.appId === applicationId);
          console.log('Application exists in user apps:', appExists);
          console.log('Available applications:', apps.map((app: any) => ({ id: app.appId, name: app.name })));

          if (!appExists) {
            console.warn('⚠️ Application ID does not belong to the authenticated user');
            // Don't throw error, just warn - proceed with ENS registration
          }
        } else {
          console.warn('⚠️ Applications endpoint failed:', appsResponse.error);
          // Don't throw error, just warn - backend might be temporarily unavailable
        }
      } catch (error) {
        console.warn('⚠️ Token validation skipped due to network error:', error);
        // Continue with ENS registration even if validation fails
      }

      // Final validation step
      setRegistrationStatus('submitting');

      console.log('Starting ENS registration...');
      console.log('Application ID:', applicationId);

      const registrationData: ENSRootRegistrationRequest = {
        ens_name: formData.ensName,
        contractAddress: formData.contractAddress,
        chain: formData.chain,
        isActive: true
      };

      // Validate that contract address matches connected wallet
      if (formData.contractAddress.toLowerCase() !== address?.toLowerCase()) {
        // Contract address validation warning (silent)
      }

      // Authenticate wallet for ENS operations (REQUIRED)

      const walletAuth = await authenticateWallet(address);

      if (!walletAuth.success) {
        throw new Error('Wallet authentication failed: ' + walletAuth.error);
      }

      console.log('✅ Wallet authenticated successfully');
      console.log('Wallet auth token length:', walletAuth.token?.length);
      console.log('🚀 Making ENS registration request with wallet auth...');
      const response = await apiService.registerENSRoot(registrationData, token, applicationId);
      console.log('ENS registration response:', response);

      if (response.success && response.data) {
        setRegistrationStatus('success');

        showToast({
          type: 'success',
          title: 'ENS Root Registered Successfully! 🎉',
          description: `${formData.ensName} has been registered for your application`
        });

        onSuccess?.(response.data.data);

        // Reset form after successful registration
        setTimeout(() => {
          setFormData({
            ensName: '',
            chain: 'sepolia',
            contractAddress: address || '',
          });
          setOwnershipVerified(null);
          setCurrentOwner(null);
          setRegistrationStatus('idle');
          setEnsNameError(null);
        }, 2000);

      } else {
        throw new Error(response.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('ENS registration error:', error);
      setRegistrationStatus('error');

      // Handle network connectivity errors
      if (error.message?.includes('Network error') || error.message?.includes('ERR_CONNECTION_REFUSED')) {
        setRegistrationError('Backend service is currently unavailable. Please try again later.');
        showToast({
          type: 'error',
          title: 'Service Unavailable',
          description: 'The Crefy Connect backend service is currently down. Please try again later.'
        });
        onError?.('Backend service is currently unavailable. Please try again later.');
        return;
      }

      // Handle wallet authentication errors specifically
      if (error.message?.includes('Wallet authentication failed')) {
        setRegistrationError('Wallet authentication failed. Please try again.');
        showToast({
          type: 'error',
          title: 'Wallet Authentication Failed',
          description: 'Failed to authenticate with your wallet. Please try again.'
        });
        onError?.('Wallet authentication failed. Please try again.');
        return;
      }

      // Handle API authentication errors
      if (error.message?.includes('Invalid token payload') || error.message?.includes('Unauthorized')) {
        setRegistrationError('Authentication failed. Please log out and log back in.');
        showToast({
          type: 'error',
          title: 'Authentication Error',
          description: 'Your session has expired. Please log out and log back in to continue.'
        });
        onError?.('Authentication failed. Please log out and log back in.');
        return;
      }

      const ensError = ENSErrorHandler.handleError(error, 'ENS Root Registration');
      setRegistrationError(ensError.userMessage);

      ENSErrorHandler.showErrorToast(ensError);
      onError?.(ensError.userMessage);
    } finally {
      setIsRegistering(false);
      if (registrationStatus !== 'success') {
        setRegistrationStatus('idle');
      }
    }
  };

  const isFormValid = formData.ensName &&
                     formData.contractAddress &&
                     formData.chain &&
                     ownershipVerified === true &&
                     !ensNameError &&
                     !isValidatingFormat;

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 max-w-none overflow-hidden">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
            <GlobeIcon className="h-5 w-5 sm:h-6 sm:w-6 text-[#4A148C]" />
            {prefilledENSName ? 'Link ENS to Application' : 'Register ENS Root Domain'}
          </CardTitle>
          <p className="text-sm text-gray-600 mt-2 leading-relaxed">
            {prefilledENSName
              ? `Link ${prefilledENSName} to your application to enable ENS functionality.`
              : 'Connect your ENS domain to enable subname creation for your application users.'
            }
          </p>
        </CardHeader>

      <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6">
        {/* Enhanced ENS Name Input */}
        <div className="space-y-3">
          <Label htmlFor="ensName" className="text-sm font-medium text-[#4A148C]">
            ENS Root Domain
          </Label>
          <div className="space-y-2">
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative flex-1">
                <Input
                  id="ensName"
                  type="text"
                  placeholder="myplatform"
                  value={formData.ensName.replace('.eth', '')}
                  onChange={(e) => handleENSNameChange(e.target.value)}
                  disabled={!!prefilledENSName}
                  className={`pr-12 transition-all duration-200 text-black ${
                    prefilledENSName
                      ? 'bg-gray-50 border-gray-200 text-gray-600'
                      : ensNameError
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : formData.ensName && !ensNameError && !isValidatingFormat
                      ? 'border-green-300 focus:border-green-500 focus:ring-green-500'
                      : 'border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20'
                  }`}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
                  {isValidatingFormat ? (
                    <div className="w-4 h-4 border-2 border-[#4A148C] border-t-transparent rounded-full animate-spin"></div>
                  ) : formData.ensName && !ensNameError ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  ) : ensNameError ? (
                    <div className="w-4 h-4 rounded-full bg-red-100 flex items-center justify-center">
                      <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                    </div>
                  ) : null}
                </div>
              </div>

              {!prefilledENSName && (
                <Button
                  onClick={verifyENSOwnership}
                  disabled={!formData.ensName || !isConnected || isVerifying || !!ensNameError || isValidatingFormat}
                  variant="outline"
                  size="sm"
                  className="w-full sm:w-auto min-h-[40px] border-[#7B1FA2]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 disabled:opacity-50 transition-all duration-200 text-black"
                >
                  {isVerifying ? (
                    <>
                      <div className="w-3 h-3 border-2 border-[#4A148C] border-t-transparent rounded-full animate-spin mr-2"></div>
                      <span className="hidden sm:inline">Verifying</span>
                      <span className="sm:hidden">Verify...</span>
                    </>
                  ) : (
                    <>
                      <ShieldCheckIcon className="mr-2 h-4 w-4" />
                      <span>Verify Ownership</span>
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* Real-time validation feedback */}
            {ensNameError && (
              <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-2">
                <div className="w-4 h-4 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                  <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                </div>
                <span>{ensNameError}</span>
              </div>
            )}

            {formData.ensName && !ensNameError && !isValidatingFormat && (
              <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 border border-green-200 rounded-lg p-2">
                <CheckCircleIcon className="h-4 w-4 flex-shrink-0" />
                <span>Valid ENS name format</span>
              </div>
            )}

            {/* Format guidelines */}
            <div className="text-xs text-gray-500 space-y-1">
              <p>ENS name requirements:</p>
              <ul className="list-disc list-inside space-y-0.5 ml-2">
                <li>3-63 characters long</li>
                <li>Lowercase letters, numbers, and hyphens only</li>
                <li>Cannot start or end with a hyphen</li>
                <li>Must be a name you own</li>
              </ul>
            </div>
          </div>

          {/* Enhanced Ownership Status */}
          {ownershipVerified !== null && (
            <div className={`p-4 rounded-lg border transition-all duration-300 ${
              ownershipVerified
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 shadow-sm'
                : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200 shadow-sm'
            }`}>
              <div className="flex items-start gap-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  ownershipVerified ? 'bg-green-100' : 'bg-red-100'
                }`}>
                  {ownershipVerified ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  ) : (
                    <div className="w-5 h-5 rounded-full bg-red-600 flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className={`text-sm font-semibold ${
                      ownershipVerified ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {ownershipVerified
                        ? (prefilledENSName ? 'ENS Ownership Pre-verified ✓' : 'Ownership Verified ✓')
                        : 'Ownership Verification Failed'
                      }
                    </span>
                    {ownershipVerified && (
                      <Badge className="bg-green-100 text-green-800 text-xs">
                        {prefilledENSName ? 'Ready to Link' : 'Ready to Register'}
                      </Badge>
                    )}
                  </div>

                  {currentOwner && (
                    <div className={`text-xs space-y-1 ${
                      ownershipVerified ? 'text-green-700' : 'text-red-700'
                    }`}>
                      <p>
                        <span className="font-medium">Current owner:</span>{' '}
                        <span className="font-mono bg-white/50 px-1 py-0.5 rounded">
                          {currentOwner.slice(0, 6)}...{currentOwner.slice(-4)}
                        </span>
                      </p>
                      {ownershipVerified && (
                        <p className="text-green-600">
                          ✓ This matches your connected wallet address
                        </p>
                      )}
                      {!ownershipVerified && address && (
                        <p className="text-red-600">
                          ✗ Your wallet: {address.slice(0, 6)}...{address.slice(-4)}
                        </p>
                      )}
                    </div>
                  )}

                  {!ownershipVerified && (
                    <div className="mt-2 text-xs text-red-600 bg-red-100/50 p-2 rounded border border-red-200">
                      <p className="font-medium mb-1">To proceed, you need to:</p>
                      <ul className="list-disc list-inside space-y-0.5">
                        <li>Own the ENS name you&apos;re trying to register</li>
                        <li>Connect the wallet that owns this ENS name</li>
                        <li>Or transfer the ENS name to your current wallet</li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Chain Selection */}
        <div className="space-y-3">
          <Label htmlFor="chain" className="text-sm font-medium text-[#4A148C]">
            Blockchain Network
          </Label>
          <Select
            className="text-black bg-white"
            id="chain"
            value={formData.chain}
            onChange={(e) => handleInputChange('chain', e.target.value as SupportedChain)}
          >
            {SUPPORTED_CHAINS.map(chain => (
              <SelectOption key={chain} value={chain}>
                {chain.charAt(0).toUpperCase() + chain.slice(1)}
                {chain === 'sepolia' && (
                  <Badge variant="secondary" className="ml-2 text-xs">Testnet</Badge>
                )}
              </SelectOption>
            ))}
          </Select>
        </div>

        {/* Contract Address */}
        <div className="space-y-3">
          <Label htmlFor="contractAddress" className="text-sm font-medium text-[#4A148C]">
            Wallet Address {prefilledENSName ? '(From ENS Verification)' : '(Auto-populated)'}
          </Label>
          <div className="relative">
            <Input
              className={`text-black pr-10 ${
                formData.contractAddress === address && address
                  ? 'border-green-300 focus:border-green-500'
                  : 'border-[#B497D6]/30 focus:border-[#4A148C]'
              }`}
              id="contractAddress"
              type="text"
              placeholder="0x..."
              value={formData.contractAddress}
              onChange={(e) => handleInputChange('contractAddress', e.target.value)}
              disabled={false}
            />
            {formData.contractAddress === address && address && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <CheckCircleIcon className="h-5 w-5 text-green-600" />
              </div>
            )}
          </div>
          <div className="space-y-1">
            <p className="text-xs text-gray-500">
              {prefilledENSName
                ? 'This wallet address was verified during ENS ownership verification and will be associated with the ENS registration.'
                : 'Your connected wallet address will be associated with the ENS registration. You can edit this if needed.'
              }
            </p>
            {formData.contractAddress === address && address && (
              <p className="text-xs text-green-600 flex items-center gap-1">
                <CheckCircleIcon className="h-3 w-3" />
                Using connected wallet address
              </p>
            )}
          </div>
        </div>

        {/* Enhanced Registration Status */}
        {registrationError && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                <div className="w-3 h-3 bg-red-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-red-800 mb-1">Registration Failed</h4>
                <p className="text-sm text-red-700">{registrationError}</p>
                <Button
                  onClick={() => setRegistrationError(null)}
                  size="sm"
                  variant="outline"
                  className="mt-2 border-red-300 hover:bg-red-100 text-red-700"
                >
                  Dismiss
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Register Button */}
        <Button
          onClick={handleRegister}
          disabled={!isFormValid || isRegistering || !isConnected || isSigning}
          className={`w-full transition-all duration-300 ${
            registrationStatus === 'success'
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105'
          } ${
            !isFormValid || isRegistering || !isConnected || isSigning
              ? 'opacity-50 cursor-not-allowed hover:scale-100 hover:shadow-none'
              : ''
          }`}
        >
          {isSigning ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Sign with Wallet...
            </>
          ) : registrationStatus === 'validating' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Validating...
            </>
          ) : registrationStatus === 'submitting' ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              {prefilledENSName ? 'Linking ENS...' : 'Registering ENS Root...'}
            </>
          ) : registrationStatus === 'success' ? (
            <>
              <CheckCircleIcon className="mr-2 h-4 w-4" />
              {prefilledENSName ? 'Successfully Linked!' : 'Successfully Registered!'}
            </>
          ) : (
            <>
              <GlobeIcon className="mr-2 h-4 w-4" />
              {prefilledENSName ? 'Link ENS to Application' : 'Register ENS Root'}
            </>
          )}
        </Button>

        {/* Authentication Error Message */}
        {registrationError && registrationError.includes('Authentication') && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800 mb-1">Authentication Required</h4>
                <p className="text-sm text-red-700 mb-3">
                  Your session has expired or your authentication token is invalid. Please log out and log back in to continue.
                </p>
                <Button
                  onClick={() => window.location.href = '/auth/signin'}
                  size="sm"
                  variant="outline"
                  className="border-red-300 hover:bg-red-100 hover:border-red-400 text-red-700"
                >
                  Go to Sign In
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Registration Progress Indicator */}
        {isRegistering && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Registration Progress</span>
              <span>
                {registrationStatus === 'validating' && 'Validating data...'}
                {registrationStatus === 'submitting' && 'Submitting to blockchain...'}
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] h-2 rounded-full transition-all duration-500 ${
                  registrationStatus === 'validating' ? 'w-1/3' :
                  registrationStatus === 'submitting' ? 'w-2/3' :
                  registrationStatus === 'success' ? 'w-full' : 'w-0'
                }`}
              ></div>
            </div>
          </div>
        )}

        {/* Backend Status Warning */}
        {registrationError && registrationError.includes('Backend service is currently unavailable') && (
          <div className="p-3 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircleIcon className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="text-red-800 font-medium mb-1">Service Temporarily Unavailable</p>
                <p className="text-red-700 mb-2">
                  The Crefy Connect backend service is currently down. Please check back in a few minutes.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Wallet Authentication Info */}
        <div className="p-3 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircleIcon className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-purple-800 font-medium mb-1">Wallet Signature Required</p>
              <p className="text-purple-700 mb-2">
                ENS registration requires wallet authentication. You&apos;ll be prompted to sign a message with your wallet to verify ownership.
              </p>
            </div>
          </div>
        </div>

        {/* Help Text */}
        <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-blue-800 font-medium mb-1">Need an ENS domain?</p>
              <p className="text-blue-700 mb-2">
                You must own a .eth domain before registering it as a root.
                Get one at app.ens.domains.
              </p>
              <Button
                onClick={() => window.open('https://app.ens.domains', '_blank')}
                size="sm"
                variant="outline"
                className="border-blue-300 hover:bg-blue-100 hover:border-blue-400 text-blue-700"
              >
                <ExternalLinkIcon className="mr-1 h-3 w-3" />
                Get ENS Domain
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
