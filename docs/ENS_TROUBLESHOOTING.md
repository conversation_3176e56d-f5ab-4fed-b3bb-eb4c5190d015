# ENS Registration Troubleshooting Guide

This guide helps you resolve common issues when registering ENS domains in Crefy Connect.

## Common Error: "Registrar creation failed"

### Quick Fixes (Try These First)

1. **Check Your Wallet Connection**
   - Ensure your wallet is connected and unlocked
   - Verify you're on the correct network (Sepolia Testnet or Ethereum Mainnet)
   - Try disconnecting and reconnecting your wallet

2. **Verify ENS Ownership**
   - Confirm you own the ENS name with the connected wallet
   - Check on [ENS App](https://app.ens.domains) that you're the owner
   - Ensure the ENS name ends with `.eth`

3. **Check ETH Balance**
   - You need at least 0.001 ETH for gas fees
   - Add more ETH to your wallet if balance is low

### Detailed Troubleshooting Steps

#### Step 1: Run Debug Test
1. Click the "🔍 Debug Test" button in the ENS registration modal
2. Check the browser console for detailed test results
3. Look for any failed tests (marked with ❌)

#### Step 2: Network Issues
**Problem**: Wrong network or network connectivity issues

**Solutions**:
- Switch to the correct network in your wallet:
  - For testing: Sepolia Testnet (Chain ID: 11155111)
  - For production: Ethereum Mainnet (Chain ID: 1)
- Check your internet connection
- Try refreshing the page

#### Step 3: Wallet Issues
**Problem**: Wallet connection or signing issues

**Solutions**:
- Unlock your wallet
- Try a different browser or incognito mode
- Clear browser cache and cookies
- Update your wallet extension
- Try a different wallet (MetaMask, WalletConnect, etc.)

#### Step 4: ENS Ownership Issues
**Problem**: You don't own the ENS name or it's not properly configured

**Solutions**:
- Verify ownership on [ENS App](https://app.ens.domains)
- If you own it but it's not wrapped, wrap it in the NameWrapper contract
- Ensure the ENS name is not expired
- Check that you're using the correct wallet address

#### Step 5: Gas and Transaction Issues
**Problem**: Transaction fails due to gas or network congestion

**Solutions**:
- Increase gas limit in your wallet
- Wait for network congestion to reduce
- Try during off-peak hours
- Ensure you have enough ETH for gas fees

#### Step 6: Smart Contract Issues
**Problem**: Factory contract or blockchain interaction issues

**Solutions**:
- Wait a few minutes and try again
- Check if the factory contract is deployed correctly
- Verify the contract address in the console logs

### Error Code Reference

| Error Code | Meaning | Solution |
|------------|---------|----------|
| `ACTION_REJECTED` | User cancelled transaction | Approve the transaction in your wallet |
| `INSUFFICIENT_FUNDS` | Not enough ETH for gas | Add more ETH to your wallet |
| `UNPREDICTABLE_GAS_LIMIT` | Transaction would fail | Check ENS ownership and network |
| `NETWORK_ERROR` | Connection issue | Check internet and try again |
| `4001` | User rejected request | Approve the transaction |

### Advanced Debugging

#### Console Logs to Check
1. Open browser developer tools (F12)
2. Go to Console tab
3. Look for these log patterns:
   - `=== FRONTEND: Creating registrar with detailed debugging ===`
   - `=== DEBUG TEST START ===`
   - Any error messages in red

#### Common Console Error Messages

**"Factory contract not deployed"**
- The smart contract isn't available on the current network
- Switch to the correct network

**"ENS ownership validation failed"**
- You don't own the ENS name
- Verify ownership and use the correct wallet

**"Signer address mismatch"**
- Wallet connection issue
- Reconnect your wallet

**"Wrong network"**
- You're on the wrong blockchain network
- Switch to Sepolia (testnet) or Mainnet

### Getting Help

If you're still experiencing issues:

1. **Collect Information**:
   - Browser and version
   - Wallet type and version
   - ENS name you're trying to register
   - Error message from console
   - Network you're using

2. **Contact Support**:
   - Include all the information above
   - Provide screenshots of error messages
   - Mention steps you've already tried

### Prevention Tips

1. **Before Starting**:
   - Ensure you have sufficient ETH balance
   - Verify you're on the correct network
   - Confirm ENS ownership
   - Use a stable internet connection

2. **During Registration**:
   - Don't close the browser tab during the process
   - Don't switch networks mid-process
   - Wait for each step to complete before proceeding

3. **Best Practices**:
   - Test with a small amount first
   - Use the debug test feature before registering
   - Keep your wallet extension updated
   - Use a reliable internet connection

### Technical Details

The ENS registration process involves:
1. Validating ENS ownership
2. Creating a subname registrar contract
3. Transferring ENS ownership to the contract
4. Storing registration details in the backend

Each step must complete successfully for the registration to work.
