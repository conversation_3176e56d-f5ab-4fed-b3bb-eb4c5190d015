/**
 * Direct contract interaction utilities for ENS operations
 * This file provides functions to interact directly with ENS contracts using ABIs
 * without relying on backend API calls for transaction preparation
 */

import { ethers } from 'ethers';
import { namehash, isValidENSName, isValid<PERSON>hain, getChainId, getENSTokenId, getSigner } from './ens-utils';
import { FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI } from './contracts/factory-contract';
import { getNameWrapperAddress, NAMEWRAPPER_CONTRACT_ABI } from './contracts/name-wrapper';

/**
 * Direct registrar creation using contract ABI
 * Returns both transaction and the created contract address
 */
export async function createRegistrarDirect(
  ensName: string,
  chain: string = 'sepolia',
  customSigner?: ethers.Signer
): Promise<{
  transaction: ethers.TransactionResponse;
  contractAddress?: string;
  receipt?: ethers.TransactionReceipt;
}> {
  console.log('=== createRegistrarDirect: Starting ===');
  console.log('ENS Name:', ensName);
  console.log('Chain:', chain);

  if (!isValidENSName(ensName)) {
    throw new Error(`Invalid ENS name format: ${ensName}`);
  }

  if (!isValidChain(chain)) {
    throw new Error(`Invalid chain name: ${chain}`);
  }

  try {
    // Get signer
    console.log('Getting signer...');
    const signer = customSigner || await getSigner();
    const signerAddress = await signer.getAddress();
    console.log('Signer address:', signerAddress);

    // Check network
    const network = await signer.provider?.getNetwork();
    console.log('Current network:', network?.name, 'chainId:', network?.chainId);

    // Verify factory contract exists
    console.log('Checking factory contract at:', FACTORY_CONTRACT_ADDRESS);
    const factoryCode = await signer.provider?.getCode(FACTORY_CONTRACT_ADDRESS);
    if (!factoryCode || factoryCode === '0x') {
      throw new Error(`Factory contract not deployed at ${FACTORY_CONTRACT_ADDRESS} on ${chain}`);
    }
    console.log('Factory contract verified, code length:', factoryCode.length);

    // Create factory contract instance
    const factoryContract = new ethers.Contract(
      FACTORY_CONTRACT_ADDRESS,
      FACTORY_CONTRACT_ABI,
      signer
    );

    // Calculate parent node hash
    const parentNode = namehash(ensName.toLowerCase());
    console.log('Parent node hash:', parentNode);

    // Test contract call first
    console.log('Testing contract call...');
    try {
      const gasEstimate = await factoryContract.createSubnameRegistrar.estimateGas(parentNode);
      console.log('Gas estimate:', gasEstimate.toString());
    } catch (gasError) {
      console.error('Gas estimation failed:', gasError);
      throw new Error(`Transaction would fail: ${gasError.reason || gasError.message}`);
    }

    // Execute the transaction
    console.log('Executing createSubnameRegistrar transaction...');
    const transaction = await factoryContract.createSubnameRegistrar(parentNode);

    console.log('Transaction sent:', transaction.hash);
    console.log('Transaction details:', {
      to: transaction.to,
      from: transaction.from,
      gasLimit: transaction.gasLimit?.toString(),
      gasPrice: transaction.gasPrice?.toString(),
      nonce: transaction.nonce
    });

    // Wait for transaction receipt
    console.log('Waiting for transaction confirmation...');
    const receipt = await transaction.wait();

    console.log('Transaction confirmed:', receipt?.hash);
    console.log('Gas used:', receipt?.gasUsed?.toString());
    console.log('Status:', receipt?.status);

    if (receipt?.status !== 1) {
      throw new Error('Transaction failed - status is not 1');
    }

    // Parse the SubnameContractCreated event to get the contract address
    let contractAddress: string | undefined;
    if (receipt && receipt.logs) {
      console.log('Parsing transaction logs, found', receipt.logs.length, 'logs');
      const iface = new ethers.Interface(FACTORY_CONTRACT_ABI);

      for (let i = 0; i < receipt.logs.length; i++) {
        const log = receipt.logs[i];
        console.log(`Log ${i}:`, {
          address: log.address,
          topics: log.topics,
          data: log.data
        });

        try {
          const parsed = iface.parseLog(log);
          console.log(`Parsed log ${i}:`, parsed);

          if (parsed && parsed.name === 'SubnameContractCreated') {
            contractAddress = parsed.args.contractAddress;
            console.log('Found SubnameContractCreated event!');
            console.log('Created contract address:', contractAddress);
            console.log('Owner:', parsed.args.owner);
            console.log('Parent node:', parsed.args.parentNode);
            break;
          }
        } catch (parseError) {
          console.log(`Could not parse log ${i}:`, parseError.message);
          continue;
        }
      }
    }

    if (!contractAddress) {
      console.warn('Could not extract contract address from transaction receipt');
      console.warn('This might indicate an issue with the event parsing or contract deployment');
    }

    console.log('=== createRegistrarDirect: Success ===');
    return { transaction, contractAddress, receipt };

  } catch (error) {
    console.error('=== createRegistrarDirect: Error ===');
    console.error('Error details:', error);

    if (error.reason) {
      console.error('Revert reason:', error.reason);
    }
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.transaction) {
      console.error('Failed transaction:', error.transaction);
    }

    throw error;
  }
}

/**
 * Direct NameWrapper transfer using contract ABI
 */
export async function transferNameWrapperDirect(
  ensName: string,
  fromAddress: string,
  toAddress: string,
  chain: string = 'sepolia',
  customSigner?: ethers.Signer
): Promise<{
  transaction: ethers.TransactionResponse;
  receipt?: ethers.TransactionReceipt;
}> {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  if (!ethers.isAddress(fromAddress)) {
    throw new Error('Invalid from address');
  }

  if (!ethers.isAddress(toAddress)) {
    throw new Error('Invalid to address');
  }

  if (!isValidChain(chain)) {
    throw new Error('Invalid chain name');
  }

  // Get signer
  const signer = customSigner || await getSigner();
  
  // Verify signer address matches fromAddress
  const signerAddress = await signer.getAddress();
  if (signerAddress.toLowerCase() !== fromAddress.toLowerCase()) {
    throw new Error(`Signer address ${signerAddress} does not match from address ${fromAddress}`);
  }

  // Get NameWrapper contract address and create instance
  const nameWrapperAddress = getNameWrapperAddress(chain);
  const nameWrapperContract = new ethers.Contract(
    nameWrapperAddress,
    NAMEWRAPPER_CONTRACT_ABI,
    signer
  );

  // Get the token ID for the ENS name
  const tokenId = getENSTokenId(ensName);

  console.log('Transferring ENS name:', ensName);
  console.log('From:', fromAddress);
  console.log('To:', toAddress);
  console.log('Token ID:', tokenId);
  console.log('NameWrapper address:', nameWrapperAddress);

  // Execute the transfer
  const transaction = await nameWrapperContract.safeTransferFrom(
    fromAddress,
    toAddress,
    BigInt(tokenId),
    1n, // amount = 1 for ENS transfers
    '0x' // empty data
  );

  console.log('Transfer transaction sent:', transaction.hash);

  // Wait for confirmation
  const receipt = await transaction.wait();
  
  console.log('Transfer transaction confirmed:', receipt?.hash);

  return { transaction, receipt };
}

/**
 * Check if user owns an ENS name in the NameWrapper
 */
export async function checkNameWrapperOwnership(
  ensName: string,
  userAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<{
  isOwner: boolean;
  actualOwner?: string;
  isWrapped: boolean;
}> {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  if (!ethers.isAddress(userAddress)) {
    throw new Error('Invalid user address');
  }

  // Get provider
  const provider = customProvider || new ethers.JsonRpcProvider(
    chain === 'mainnet' ? 'https://eth.llamarpc.com' : 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY'
  );

  // Get NameWrapper contract
  const nameWrapperAddress = getNameWrapperAddress(chain);
  const nameWrapperContract = new ethers.Contract(
    nameWrapperAddress,
    NAMEWRAPPER_CONTRACT_ABI,
    provider
  );

  const tokenId = getENSTokenId(ensName);

  try {
    // Check if the name is wrapped (has a balance > 0)
    const balance = await nameWrapperContract.balanceOf(nameWrapperAddress, BigInt(tokenId));
    const isWrapped = balance > 0n;

    if (!isWrapped) {
      return { isOwner: false, isWrapped: false };
    }

    // Get the actual owner
    const actualOwner = await nameWrapperContract.ownerOf(BigInt(tokenId));
    const isOwner = actualOwner.toLowerCase() === userAddress.toLowerCase();

    return { isOwner, actualOwner, isWrapped };

  } catch (error) {
    console.error('Error checking NameWrapper ownership:', error);
    return { isOwner: false, isWrapped: false };
  }
}

/**
 * Get registrar contracts created by a user
 */
export async function getUserRegistrarContracts(
  userAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<string[]> {
  if (!ethers.isAddress(userAddress)) {
    throw new Error('Invalid user address');
  }

  // Get provider
  const provider = customProvider || new ethers.JsonRpcProvider(
    chain === 'mainnet' ? 'https://eth.llamarpc.com' : 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY'
  );

  // Create factory contract instance
  const factoryContract = new ethers.Contract(
    FACTORY_CONTRACT_ADDRESS,
    FACTORY_CONTRACT_ABI,
    provider
  );

  try {
    const contracts = await factoryContract.getContractsByOwner(userAddress);
    return contracts;
  } catch (error) {
    console.error('Error getting user registrar contracts:', error);
    return [];
  }
}

/**
 * Validate ENS name ownership before operations
 */
export async function validateENSOwnership(
  ensName: string,
  userAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<{
  isValid: boolean;
  error?: string;
  ownershipType?: 'registry' | 'namewrapper';
  actualOwner?: string;
}> {
  try {
    // First check NameWrapper ownership
    const nameWrapperCheck = await checkNameWrapperOwnership(ensName, userAddress, chain, customProvider);
    
    if (nameWrapperCheck.isWrapped) {
      return {
        isValid: nameWrapperCheck.isOwner,
        error: nameWrapperCheck.isOwner ? undefined : `ENS name is owned by ${nameWrapperCheck.actualOwner}`,
        ownershipType: 'namewrapper',
        actualOwner: nameWrapperCheck.actualOwner
      };
    }

    // If not wrapped, check registry ownership
    const { getENSOwner } = await import('./ens-utils');
    const registryOwner = await getENSOwner(ensName, customProvider, getChainId(chain));
    
    if (!registryOwner) {
      return {
        isValid: false,
        error: 'ENS name not found or has no owner'
      };
    }

    const isRegistryOwner = registryOwner.toLowerCase() === userAddress.toLowerCase();
    
    return {
      isValid: isRegistryOwner,
      error: isRegistryOwner ? undefined : `ENS name is owned by ${registryOwner}`,
      ownershipType: 'registry',
      actualOwner: registryOwner
    };

  } catch (error) {
    return {
      isValid: false,
      error: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
