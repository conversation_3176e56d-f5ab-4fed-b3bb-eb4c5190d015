/**
 * ENS Error Recovery and Retry Utilities
 * Provides functions to handle common ENS registration errors and implement retry logic
 */

import { ethers } from 'ethers';

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
}

export interface ErrorRecoveryResult {
  success: boolean;
  error?: Error;
  retryCount: number;
  totalTime: number;
}

/**
 * Default retry configuration for ENS operations
 */
export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2
};

/**
 * Sleep utility for retry delays
 */
const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Calculate delay for retry attempt with exponential backoff
 */
function calculateDelay(attempt: number, options: RetryOptions): number {
  const delay = options.baseDelay * Math.pow(options.backoffMultiplier, attempt);
  return Math.min(delay, options.maxDelay);
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code;

  // Network-related errors that are retryable
  const retryablePatterns = [
    'network error',
    'timeout',
    'connection',
    'fetch',
    'rate limit',
    'too many requests',
    'server error',
    'internal error',
    'temporary failure',
    'try again'
  ];

  // Error codes that are retryable
  const retryableCodes = [
    'NETWORK_ERROR',
    'TIMEOUT',
    'SERVER_ERROR',
    'CALL_EXCEPTION', // Sometimes retryable for network issues
    -32603, // Internal error
    -32005, // Rate limiting
    429 // Too many requests
  ];

  // Check for retryable patterns in error message
  const hasRetryablePattern = retryablePatterns.some(pattern => 
    errorMessage.includes(pattern)
  );

  // Check for retryable error codes
  const hasRetryableCode = retryableCodes.includes(errorCode);

  // Don't retry user-rejected transactions or insufficient funds
  const nonRetryablePatterns = [
    'user rejected',
    'action_rejected',
    'insufficient funds',
    'not the owner',
    'invalid ens name',
    'wrong network'
  ];

  const hasNonRetryablePattern = nonRetryablePatterns.some(pattern =>
    errorMessage.includes(pattern)
  );

  return (hasRetryablePattern || hasRetryableCode) && !hasNonRetryablePattern;
}

/**
 * Retry wrapper for async functions with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {},
  operationName: string = 'operation'
): Promise<ErrorRecoveryResult & { result?: T }> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options };
  const startTime = Date.now();
  let lastError: Error | undefined;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      console.log(`Attempting ${operationName} (attempt ${attempt + 1}/${config.maxRetries + 1})`);
      
      const result = await operation();
      const totalTime = Date.now() - startTime;
      
      console.log(`${operationName} succeeded on attempt ${attempt + 1} after ${totalTime}ms`);
      
      return {
        success: true,
        retryCount: attempt,
        totalTime,
        result
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      console.warn(`${operationName} failed on attempt ${attempt + 1}:`, lastError.message);
      
      // Don't retry on the last attempt or if error is not retryable
      if (attempt === config.maxRetries || !isRetryableError(lastError)) {
        break;
      }
      
      const delay = calculateDelay(attempt, config);
      console.log(`Retrying ${operationName} in ${delay}ms...`);
      await sleep(delay);
    }
  }

  const totalTime = Date.now() - startTime;
  
  return {
    success: false,
    error: lastError,
    retryCount: config.maxRetries,
    totalTime
  };
}

/**
 * Specific retry wrapper for ENS registrar creation
 */
export async function createRegistrarWithRetry(
  ensName: string,
  chain: string,
  options: Partial<RetryOptions> = {}
): Promise<ErrorRecoveryResult & { result?: any }> {
  return withRetry(
    async () => {
      const { createRegistrarDirect } = await import('./ens-contract-utils');
      return createRegistrarDirect(ensName, chain);
    },
    options,
    `ENS registrar creation for ${ensName}`
  );
}

/**
 * Specific retry wrapper for ENS ownership validation
 */
export async function validateOwnershipWithRetry(
  ensName: string,
  userAddress: string,
  chain: string,
  options: Partial<RetryOptions> = {}
): Promise<ErrorRecoveryResult & { result?: any }> {
  return withRetry(
    async () => {
      const { validateENSOwnership } = await import('./ens-contract-utils');
      return validateENSOwnership(ensName, userAddress, chain);
    },
    options,
    `ENS ownership validation for ${ensName}`
  );
}

/**
 * Specific retry wrapper for ENS transfer
 */
export async function transferENSWithRetry(
  ensName: string,
  fromAddress: string,
  toAddress: string,
  chain: string,
  options: Partial<RetryOptions> = {}
): Promise<ErrorRecoveryResult & { result?: any }> {
  return withRetry(
    async () => {
      const { transferNameWrapperDirect } = await import('./ens-contract-utils');
      return transferNameWrapperDirect(ensName, fromAddress, toAddress, chain);
    },
    options,
    `ENS transfer for ${ensName}`
  );
}

/**
 * Enhanced error message generator
 */
export function getEnhancedErrorMessage(error: any): string {
  if (!error) return 'Unknown error occurred';

  const errorMessage = error.message || String(error);
  const errorCode = error.code;

  // Map common error codes to user-friendly messages
  const errorCodeMap: Record<string, string> = {
    'ACTION_REJECTED': 'Transaction was cancelled. Please try again and approve the transaction in your wallet.',
    'INSUFFICIENT_FUNDS': 'Insufficient ETH balance for gas fees. Please add more ETH to your wallet.',
    'UNPREDICTABLE_GAS_LIMIT': 'Gas estimation failed. This usually means the transaction would fail. Please check your ENS ownership and try again.',
    'NETWORK_ERROR': 'Network connection error. Please check your internet connection and try again.',
    'CALL_EXCEPTION': 'Smart contract call failed. Please check your inputs and try again.',
    '4001': 'Transaction was rejected by user. Please approve the transaction to continue.'
  };

  if (errorCode && errorCodeMap[errorCode]) {
    return errorCodeMap[errorCode];
  }

  // Check for common error patterns in message
  const lowerMessage = errorMessage.toLowerCase();
  
  if (lowerMessage.includes('insufficient funds')) {
    return 'Insufficient ETH balance for gas fees. Please add more ETH to your wallet.';
  }
  
  if (lowerMessage.includes('user rejected') || lowerMessage.includes('user denied')) {
    return 'Transaction was cancelled. Please try again and approve the transaction in your wallet.';
  }
  
  if (lowerMessage.includes('not the owner') || lowerMessage.includes('not own')) {
    return 'You do not own this ENS name. Please check the ENS name or connect the correct wallet.';
  }
  
  if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
    return 'Network connection issue. Please check your internet connection and try again.';
  }
  
  if (lowerMessage.includes('gas')) {
    return 'Gas estimation failed. The transaction would likely fail. Please check your ENS ownership and try again.';
  }

  if (lowerMessage.includes('wrong network')) {
    return 'Wrong network. Please switch to the correct network in your wallet.';
  }

  // Return original message if no specific mapping found
  return errorMessage;
}

/**
 * Check if the current error suggests a specific recovery action
 */
export function getRecoveryAction(error: any): string | null {
  if (!error) return null;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code;

  if (errorCode === 'ACTION_REJECTED' || errorCode === 4001 || errorMessage.includes('user rejected')) {
    return 'Try the transaction again and approve it in your wallet.';
  }

  if (errorCode === 'INSUFFICIENT_FUNDS' || errorMessage.includes('insufficient funds')) {
    return 'Add more ETH to your wallet and try again.';
  }

  if (errorMessage.includes('wrong network')) {
    return 'Switch to the correct network in your wallet.';
  }

  if (errorMessage.includes('not the owner')) {
    return 'Verify you own the ENS name and are using the correct wallet.';
  }

  if (errorMessage.includes('network') || errorMessage.includes('connection')) {
    return 'Check your internet connection and try again.';
  }

  if (isRetryableError(error)) {
    return 'This appears to be a temporary issue. Try again in a few moments.';
  }

  return null;
}
