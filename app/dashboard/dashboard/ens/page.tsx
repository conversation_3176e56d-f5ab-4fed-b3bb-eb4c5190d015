'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/lib/toast-context";
import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import {
  WalletIcon,
  CheckCircleIcon,
  GlobeIcon,
  ArrowUpDownIcon,
  SettingsIcon,
  ArrowRightIcon
} from "lucide-react";

import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper";
import { ENSRegistrationModal } from "@/components/ens/ens-registration-modal";
import { ENSDashboard } from "@/components/ens/ens-dashboard";
import { ENSTransferFunctions } from "@/components/ens/ens-transfer-functions-simple";
import { ENSSubnameRegistrationFlow } from "@/components/ens/ens-subname-registration-flow";
import { ApplicationSelection } from "@/components/ens/application-selection";
import { ApplicationWithApiKey } from "@/lib/api";
import { ENSConnection } from "@/lib/types/ens";
import { Button } from "@/components/ui/button";
import { PlusIcon, UserPlusIcon } from "lucide-react";

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [selectedApplication, setSelectedApplication] = useState<ApplicationWithApiKey | null>(null);
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [existingENSData, setExistingENSData] = useState<{
    ensName: string;
    contractAddress: string;
    isActive: boolean;
  } | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [showSubnameFlow, setShowSubnameFlow] = useState(false);

  // Load existing ENS data from API using GET /ens/name endpoint
  const loadENSData = useCallback(async () => {
    if (!token || !selectedApplication) return;

    try {
      // Check if app already has an ENS name registered using the specific endpoint
      const response = await apiService.getENSName(token, selectedApplication.appId);

      if (response.success && response.data) {
        // Validate the response data structure
        const { ensName, contractAddress, isActive } = response.data;

        if (ensName && contractAddress) {
          setExistingENSData({
            ensName,
            contractAddress,
            isActive: isActive ?? true
          });

          // Create a connection object for display with proper null checks
          const connection: ENSConnection = {
            id: `ens-${selectedApplication.appId}-${Date.now()}`,
            projectId: selectedApplication.appId,
            appId: selectedApplication.appId,
            ensName: ensName,
            owner: contractAddress, // Use contract address as owner for display
            connectedAt: new Date().toISOString(),
            isActive: isActive ?? true,
            contractAddress: contractAddress,
            chain: 'sepolia' // Default to sepolia, could be enhanced to detect from API
          };
          setEnsConnections([connection]);
          setShowDashboard(true);
        } else {
          // Invalid data structure, show empty state
          console.warn('Invalid ENS data structure received:', response.data);
          setExistingENSData(null);
          setEnsConnections([]);
          setShowDashboard(false);
        }
      } else {
        // No existing ENS registration, show empty state
        setExistingENSData(null);
        setEnsConnections([]);
        setShowDashboard(false);
      }
    } catch (error) {
      console.error('Failed to load ENS data:', error);
      // On error, assume no existing registration and show empty state
      setExistingENSData(null);
      setEnsConnections([]);
      setShowDashboard(false);
    }
  }, [token, selectedApplication]);

  // Auto-load ENS data when selectedApplication changes
  useEffect(() => {
    if (selectedApplication) {
      loadENSData();
    }
  }, [selectedApplication, loadENSData]);

  const handleApplicationSelect = (_applicationId: string, application: ApplicationWithApiKey) => {
    setSelectedApplication(application);
    showToast({
      type: 'success',
      title: 'Application Selected',
      description: `Selected ${application.name} for ENS integration`
    });
  };

  const handleENSRegistrationComplete = (data: any) => {
    // Refresh ENS data after successful registration
    setIsModalOpen(false);
    loadENSData();
    showToast({
      type: 'success',
      title: 'ENS Registration Complete! 🎉',
      description: 'Your ENS domain has been successfully registered!'
    });
  };

  const handleSubnameRegistrationComplete = (data: any) => {
    // Refresh ENS data after successful subname registration
    setShowSubnameFlow(false);
    loadENSData();
    showToast({
      type: 'success',
      title: 'Subname Registration Complete! 🎉',
      description: 'Your subname registration flow has been completed successfully!'
    });
  };

  // Check if we should show dashboard, subname flow, or empty state
  const shouldShowDashboard = selectedApplication && (showDashboard || ensConnections.length > 0);
  const shouldShowSubnameFlow = selectedApplication && showSubnameFlow;

  return (
    <DashboardLayoutWrapper title="ENS Integration">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                  ENS Integration
                </h1>
                <p className="text-gray-600 mt-2">
                  Manage your ENS domains and enable subname claiming for your applications
                </p>
              </div>

              {selectedApplication && (
                <Button
                  onClick={() => setIsModalOpen(true)}
                  className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] text-white"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Register New ENS Domain
                </Button>
              )}
            </div>
          </div>

          {/* Main Content */}
          {!selectedApplication ? (
            /* Application Selection */
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
                  <GlobeIcon className="h-5 w-5" />
                  Select Application
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  Choose which application you want to manage ENS domains for
                </p>
              </CardHeader>
              <CardContent>
                <ApplicationSelection
                  onApplicationSelect={handleApplicationSelect}
                  selectedApplicationId={selectedApplication?.appId ? String(selectedApplication.appId) : undefined}
                />
              </CardContent>
            </Card>
          ) : shouldShowSubnameFlow ? (
            /* Subname Registration Flow */
            <ENSSubnameRegistrationFlow
              selectedApplication={selectedApplication}
              onSuccess={handleSubnameRegistrationComplete}
              onCancel={() => setShowSubnameFlow(false)}
            />
          ) : shouldShowDashboard ? (
            /* ENS Dashboard */
            <ENSDashboard
              ensConnections={ensConnections}
              selectedApplication={selectedApplication}
            />
          ) : (
            /* Empty State */
            <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
              <CardContent className="text-center py-16">
                <GlobeIcon className="h-24 w-24 mx-auto text-gray-300 mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  No ENS Domains Registered
                </h3>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  Get started by registering your first ENS domain for <strong>{selectedApplication.name}</strong>.
                  This will enable subname claiming functionality for your users.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={() => setIsModalOpen(true)}
                    className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] text-white px-8 py-3 text-lg"
                  >
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Register ENS Domain
                  </Button>
                  <Button
                    onClick={() => setShowSubnameFlow(true)}
                    variant="outline"
                    className="border-[#4A148C] text-[#4A148C] hover:bg-[#4A148C] hover:text-white px-8 py-3 text-lg"
                  >
                    <UserPlusIcon className="h-5 w-5 mr-2" />
                    Subname Registration Flow
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* ENS Registration Modal */}
          <ENSRegistrationModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            onSuccess={handleENSRegistrationComplete}
          />
        </div>
      </div>
    </DashboardLayoutWrapper>
  );
}
